'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import classNames from '@utils/classNames';
import useMediaQueryState from '@hooks/useMediaQueryState';
import styles from './ImageWithBlurPreview.module.css';

export default function ImageWithBlurPreview({
  data,
  mobileData,
  width,
  height,
  fill,
  quality,
  priority,
  unoptimized,
  loading,
  mainClass,
  prefferedSize,
}: {
  data: any;
  mobileData?: any;
  width?: any;
  height?: any;
  fill?: any;
  unoptimized?: any;
  quality?: any;
  priority?: any;
  loading?: any;
  mainClass: any;
  prefferedSize?: 'large' | 'medium' | 'small';
}) {
  const [isImageLoaded, setIsImageLoaded] = useState(false);
  const [mounted, setMounted] = useState(false);

  // Use media query to detect mobile devices
  const isMobile = useMediaQueryState({ query: '(max-width: 768px)' });

  useEffect(() => {
    setMounted(true);
  }, []);

  // Determine which image data to use
  const imageData = mounted && isMobile && mobileData ? mobileData : data;

  // Don't render anything until mounted to prevent hydration mismatch
  if (!mounted) {
    return null;
  }

  return (
    <>
      {!isImageLoaded && (
        <Image
          src={imageData?.formats?.thumbnail?.url}
          alt={imageData?.alternativeText || 'Hero image'}
          width={width}
          height={height}
          fill={fill}
          className={classNames(mainClass, 'blur')}
          priority={priority}
          loading={loading}
        />
      )}
      <Image
        src={
          prefferedSize === 'large'
            ? imageData?.format?.large?.url || imageData?.formats?.large?.url || imageData?.url
            : prefferedSize === 'medium'
              ? imageData?.format?.medium?.url ||
              imageData?.formats?.medium?.url ||
              imageData?.format?.large?.url ||
              imageData?.formats?.large?.url ||
              imageData?.url
              : prefferedSize === 'small'
                ? imageData?.format?.small?.url ||
                imageData?.formats?.small?.url ||
                imageData?.format?.medium?.url ||
                imageData?.formats?.medium?.url ||
                imageData?.format?.large?.url ||
                imageData?.formats?.large?.url ||
                imageData?.url
                : imageData?.url
        }
        width={width}
        height={height}
        fill={fill}
        alt={imageData?.alternativeText || 'Hero Image'}
        className={classNames(
          mainClass,
          isImageLoaded ? styles.image_visible : styles.image_hidden,
        )}
        quality={quality}
        priority={priority}
        loading={loading}
        unoptimized={unoptimized}
        onLoad={() => setIsImageLoaded(true)}
      />
    </>
  );
}
