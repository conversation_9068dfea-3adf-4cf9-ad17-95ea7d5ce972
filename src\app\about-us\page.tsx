import CTA from '@components/CTA';
import HeroSection from '@components/HeroSection';
import MeetOurTeam from '@components/MeetOurTeam';
import OurStory from '@components/OurStory';
import PrAndNews from '@components/PrAndNews';
import RichText from '@components/RichText';
import TrustedPartners from '@components/TrustedPartners';
import VisionMission from '@components/VisionMission';
import seoSchema from '@utils/seoSchema';
import getSectionId from '@utils/getSectionId';
import TitleDescription from '@components/TitleDescription';
import fetchFromStrapi from '@utils/fetchFromStrapi';
import RichResults from '@components/RichResults';

async function getHeaderData() {
  return await fetchFromStrapi(
    'header',
    'populate=logo&populate=logo.image&populate=menu.subMenu&populate=menu.subMenu.image&populate=menu.subLinks&populate=menu.button&populate=menu.titleDescription',
  );
}

async function fetchAboutUsPageData() {
  return await fetchFromStrapi(
    'about-us',
    'populate=hero_section.image,hero_section.mobile_image&populate=rich_text&populate=our_story.cards&populate=services_delivery_process.image&populate=vision_mission.image&populate=pr_and_news.cards.prs.image&populate=pr_and_news.cards.news.image&populate=download_our_brand&populate=meet_our_people.our_people.logo&populate=meet_our_people.our_people.image&populate=featured_on.partnersLogo.images,build_careers.emp_details.image,seo.schema',
  );
}

export async function generateMetadata() {
  const seoFetchedData = await fetchFromStrapi(
    'about-us',
    'populate=seo&populate=seo.image&populate=seo.metaProperties&populate=seo.keywords&populate=seo.schema',
  );

  const seoData = seoFetchedData?.data?.attributes?.seo;
  return seoSchema(seoData);
}

export default async function AboutUs() {
  const aboutUsPageData = await fetchAboutUsPageData();
  const headerData = await getHeaderData();

  const heroSectionRefLink = getSectionId(
    headerData?.data?.attributes?.menu[4]?.subLinks[0]?.link,
  );
  const howWeWorkRefLink = getSectionId(
    headerData?.data?.attributes?.menu[4]?.subLinks[1]?.link,
  );
  const leadershipTeamRefLink = getSectionId(
    headerData?.data?.attributes?.menu[4]?.subLinks[2]?.link,
  );
  const inNewsRefLink = getSectionId(
    headerData?.data?.attributes?.menu[4]?.subLinks[3]?.link,
  );
  return (
    <>
      {aboutUsPageData?.data?.attributes?.seo && (
        <RichResults data={aboutUsPageData?.data?.attributes?.seo} />
      )}

      {aboutUsPageData?.data?.attributes?.hero_section && (
        <HeroSection
          heroData={aboutUsPageData?.data?.attributes?.hero_section}
          variant="about_us"
        />
      )}
      {aboutUsPageData?.data?.attributes?.rich_text && (
        <RichText richTextData={aboutUsPageData?.data?.attributes?.rich_text} />
      )}
      {aboutUsPageData?.data?.attributes?.vision_mission && (
        <VisionMission
          visionMissionData={aboutUsPageData?.data?.attributes?.vision_mission}
        />
      )}
      {aboutUsPageData?.data?.attributes?.featured_on && (
        <TrustedPartners
          data={aboutUsPageData?.data?.attributes?.featured_on}
        />
      )}
      {aboutUsPageData?.data?.attributes?.our_story && (
        <div id={heroSectionRefLink}>
          <OurStory data={aboutUsPageData?.data?.attributes?.our_story} />
        </div>
      )}
      {aboutUsPageData?.data?.attributes?.services_delivery_process && (
        <div id={howWeWorkRefLink}>
          <TitleDescription
            dataTitleDescription={
              aboutUsPageData?.data?.attributes?.services_delivery_process
            }
            variant="with_image"
          />
        </div>
      )}
      {aboutUsPageData?.data?.attributes?.meet_our_people && (
        <div id={leadershipTeamRefLink}>
          <MeetOurTeam
            meetOurTeamData={aboutUsPageData?.data?.attributes?.meet_our_people}
            variant="about-us"
          />
        </div>
      )}

      {aboutUsPageData?.data?.attributes?.build_careers && (
        <TitleDescription
          dataTitleDescription={
            aboutUsPageData?.data?.attributes?.build_careers
          }
          variant="employee"
        />
      )}
      {aboutUsPageData?.data?.attributes?.pr_and_news && (
        <div id={inNewsRefLink}>
          <PrAndNews data={aboutUsPageData?.data?.attributes?.pr_and_news} />
        </div>
      )}
      {aboutUsPageData?.data?.attributes?.download_our_brand && (
        <CTA
          data={aboutUsPageData?.data?.attributes?.download_our_brand}
          variant="downloadOurBrand"
        />
      )}
    </>
  );
}
