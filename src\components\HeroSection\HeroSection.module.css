@value variables: "@styles/variables.module.css";
@value colorBlack, colorWhite, brandColorThree, fifteenSpace, grayBorder from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-md, breakpoint-xl-2000, breakpoint-sm-450, breakpoint-sm-550, breakpoint-sm-320, breakpoint-xl-1024, breakpoint-xl-1440, breakpoint-lg, breakpoint-lg-991px from breakpoints;

.main_container {
  position: relative;
  overflow: hidden;
}

.inner_container {
  display: flex;
  flex-direction: column;
  gap: 167px;
  min-height: 741px;
  max-width: 50%;
  padding: 20px 0 0 120px;
  position: relative;
  z-index: 10;

  @media screen and (max-width: breakpoint-xl-2000) {
    max-width: 70%;
  }

  @media screen and (max-width: 768px) {
    max-width: 100%;
    padding: 20px 30px 0 30px !important;
  }

  @media screen and (max-width: 450px) {
    padding: 20px 16px 0 16px !important;
  }
}

.inner_container_resources {
  display: flex;
  flex-direction: column;
  gap: 74px;
  min-height: 441px;
  max-width: 803px;
  padding: 20px 0 0 120px;
  position: relative;
  z-index: 10;

  @media screen and (max-width: 768px) {
    padding: 20px 30px 0 30px;
  }

  @media screen and (max-width: 450px) {
    padding: 20px 16px 0 16px;
  }
}

.inner_container_partners {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 167px;
  min-height: 741px;
  max-width: 50%;
  padding: 20px 0 0 120px;
  position: relative;
  z-index: 10;

  @media screen and (max-width: breakpoint-xl-2000) {
    max-width: 70%;
  }

  @media screen and (max-width: 768px) {
    max-width: 100%;
    padding: 20px 30px 0 30px !important;
  }

  @media screen and (max-width: 450px) {
    padding: 20px 16px 0 16px !important;
  }
}

.section {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.section_without_breadcrumbs {
  margin-top: 188px;
  display: flex;
  flex-direction: column;
  gap: 30px;
  font-size: 20px;
}

.tag {
  color: colorWhite;

  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  background-color: colorBlack;
  padding: 2px 6px;
  width: fit-content;
  border-radius: 3px;
}

.background_image {
  background-color: colorBlack;
  object-fit: cover;
  transition: transform 0.5s ease;
  z-index: -1;
}

/* Mobile-specific styles for better image display */
@media screen and (max-width: 768px) {
  .background_image {
    object-fit: contain;
    object-position: center;
  }

  .main_container {
    min-height: auto;
    /* Ensure mobile images are fully visible */
    display: flex;
    flex-direction: column;
  }

  /* Adjust inner container for mobile - subtle gradient overlay for text readability */
  .inner_container,
  .inner_container_resources,
  .inner_container_partners,
  .inner_container_ai_readiness {
    position: relative;
    z-index: 10;
  }

  /* Add subtle gradient overlay behind text content only */
  .inner_container::before,
  .inner_container_resources::before,
  .inner_container_partners::before,
  .inner_container_ai_readiness::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(0, 0, 0, 0.3) 0%,
      rgba(0, 0, 0, 0.1) 50%,
      transparent 100%
    );
    border-radius: 8px;
    z-index: -1;
  }

  /* Add text shadow for additional readability */
  .inner_container .title,
  .inner_container_resources .title,
  .inner_container_partners .title,
  .inner_container_ai_readiness .heading {
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);
  }

  .inner_container .hero_desc,
  .inner_container_resources .hero_desc,
  .inner_container_partners .hero_desc,
  .inner_container_ai_readiness .description {
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  }
}

/* Additional mobile breakpoints for better responsiveness */
@media screen and (max-width: 480px) {
  .background_image {
    object-fit: contain;
    object-position: center top;
    max-height: 50vh;
  }

  .main_container {
    min-height: 60vh;
  }

  /* Adjust gradient overlay for smaller screens */
  .inner_container::before,
  .inner_container_resources::before,
  .inner_container_partners::before,
  .inner_container_ai_readiness::before {
    background: linear-gradient(
      135deg,
      rgba(0, 0, 0, 0.25) 0%,
      rgba(0, 0, 0, 0.08) 50%,
      transparent 100%
    );
  }
}

@media screen and (max-width: 320px) {
  .background_image {
    object-fit: contain;
    object-position: center top;
    max-height: 40vh;
  }

  /* Even more subtle overlay for very small screens */
  .inner_container::before,
  .inner_container_resources::before,
  .inner_container_partners::before,
  .inner_container_ai_readiness::before {
    background: linear-gradient(
      135deg,
      rgba(0, 0, 0, 0.2) 0%,
      rgba(0, 0, 0, 0.05) 50%,
      transparent 100%
    );
  }
}

.main_container:hover .background_image {
  transform: scale(1.1);
}

.title > h1 {
  font-size: 78px;
  font-style: normal;
  font-weight: 600;
  line-height: 113%;
  color: colorWhite;

  @media screen and (max-width: breakpoint-md) {
    font-size: 64px;
    line-height: 76.8px;
    letter-spacing: 1.28px;
  }

  @media screen and (max-width: 450px) {
    font-size: 44px;
    line-height: 118%;
    letter-spacing: -1.76px;
  }
}

.hero_desc {
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
  color: colorWhite;
}

.btn {
  height: 62px;
  width: 192px;
  font-size: 20px;
  padding: 16px 36px;
}

.inner_container_ai_readiness {
  max-width: 60%;
  padding: 70px 0 105px 138px;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 10;

  @media screen and (max-width: breakpoint-xl-1440) {
    padding: 0 0 91px 112px;
  }

  @media screen and (max-width: 1024px) {
    max-width: 100%;
    padding: 24px 20px;
  }

  @media screen and (max-width: 450px) {
    padding: 24px 4px;
  }
}

.heading > h1 {
  font-weight: 700;
  font-size: 48px;
  line-height: 129%;
  color: colorWhite;
}

.description {
  font-weight: 400;
  font-size: 22px;
  line-height: 160%;
  color: colorWhite;
}

.description > p {
  margin: 0;
}

.cta {
  font-weight: 600;
  font-size: 20px;
  line-height: 100%;
  width: 260px;
  height: 62px;
  padding: 16px 36px;
  border-radius: 6px;
  border-width: 2px;
}
