import HomeHeroSetion from '@components/HomeHeroSection';
import TrustedPartners from '@components/TrustedPartners';
import StatisticsCard from '@components/StatisticsCard';
import Testimonial from '@components/Testimonial';
import ServicesCard from '@components/ServicesCard';
import IndustriesCard from '@components/IndustriesCard';
import CaseStudyCard from '@components/CaseStudyCard';
import Insights from '@components/Insights';
import AwardsRecognition from '@components/AwardsRecognition';
import ContactUsForm from '@components/ContactUsForm';
import seoSchema from '@utils/seoSchema';
import fetchFromStrapi from '@utils/fetchFromStrapi';
import RichResults from '@components/RichResults';

async function fetchHomepageData() {
  const query = `populate=hero_section.title_description,hero_section.image,hero_section.mobile_image,hero_section.open_link_in_new_tabour_services.our_services,our_services.ourServicesCard,our_services.ourServicesCard.cardImage,insights,insights.circular_text_image,insights.blogs.heroSection_image,Company_Statistics.title,Company_Statistics.statisticsCards&populate=case_study_cards.case_study_relation.preview.preview_background_image,case_study_cards.case_study_relation.hero_section.global_services,Industries.backgroundImage,Industries.industriesCardsBox.button,Industries.industriesCardsBox.backgroundImage,seo.schema`;
  return await fetchFromStrapi('home-page', query);
}

async function getTrustedPartnersData() {
  const query = `populate=trustedPartner.title&populate=trustedPartner.partnersLogo.images`;
  return await fetchFromStrapi('trusted-partner', query);
}

async function getTestimonialsData() {
  const query = `populate=testimonials,testimonials.testimonials_slider,testimonials.testimonials_slider.image,testimonials_slider.testimonial_video_link,testimonials.testimonial_playbtn_logo,testimonials.circular_text_line_svg`;
  return await fetchFromStrapi('testimonial', query);
}

async function getAwardsData() {
  const query = `populate=awards.awards_box.image`;
  return await fetchFromStrapi('award', query);
}

async function getFormData() {
  const query = `populate=form.formFields&populate=form.button`;
  return await fetchFromStrapi('form', query);
}

export async function generateMetadata({ }) {
  const seoFetchedData = await fetchFromStrapi(
    'home-page',
    'populate=seo&populate=seo.image&populate=seo.metaProperties&populate=seo.keywords,seo.schema',
  );
  const seoData = seoFetchedData?.data?.attributes?.seo;

  return seoSchema(seoData);
}
export default async function Home() {
  const homeData = await fetchHomepageData();
  const trustedPartner = await getTrustedPartnersData();
  const testimonials = await getTestimonialsData();
  const awards = await getAwardsData();
  const formData = await getFormData();

  return (
    <>
      {homeData?.data?.attributes?.seo && (
        <RichResults data={homeData?.data?.attributes?.seo} />
      )}

      {homeData && <HomeHeroSetion slides={homeData} varaint="primary" />}

      {trustedPartner?.data?.attributes?.trustedPartner && (
        <TrustedPartners
          data={trustedPartner?.data?.attributes?.trustedPartner}
        />
      )}

      {homeData?.data?.attributes?.Company_Statistics && (
        <StatisticsCard
          statisticsCardData={homeData?.data?.attributes?.Company_Statistics}
        />
      )}

      {testimonials?.data?.attributes?.testimonials && (
        <Testimonial data={testimonials?.data?.attributes?.testimonials} />
      )}

      {homeData?.data?.attributes?.our_services && (
        <ServicesCard
          data={homeData?.data?.attributes?.our_services}
          variant="showTextOnHover"
        />
      )}

      {homeData.data.attributes.Industries && (
        <IndustriesCard
          industriesCardData={homeData.data.attributes.Industries}
        />
      )}

      {homeData?.data?.attributes?.case_study_cards && (
        <CaseStudyCard
          case_study={homeData?.data?.attributes?.case_study_cards}
        />
      )}

      {homeData?.data?.attributes?.insights && (
        <Insights data={homeData?.data?.attributes?.insights} />
      )}

      {awards?.data?.attributes?.awards && (
        <AwardsRecognition
          awardsRecognitionData={awards?.data?.attributes?.awards}
        />
      )}

      {formData?.data?.attributes?.form && (
        <ContactUsForm
          formData={formData?.data?.attributes?.form}
          source="Home"
        />
      )}
    </>
  );
}
